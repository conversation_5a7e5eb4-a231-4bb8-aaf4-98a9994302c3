// 部署脚本 - 帮助集成优化版 handleSession
// 使用方法: node deploy_optimized.js

import fs from 'fs';
import path from 'path';

const BACKUP_SUFFIX = '.backup';
const OPTIMIZED_MARKER = '// OPTIMIZED_HANDLER_INTEGRATED';

class DeploymentHelper {
    constructor() {
        this.workingDir = process.cwd();
        this.workerFile = path.join(this.workingDir, '_worker.js');
        this.optimizedFile = path.join(this.workingDir, 'handleSession_optimized.js');
        this.backupFile = this.workerFile + BACKUP_SUFFIX;
    }

    // 检查文件是否存在
    checkFiles() {
        console.log('🔍 检查文件...');
        
        const checks = [
            { file: this.workerFile, name: '_worker.js' },
            { file: this.optimizedFile, name: 'handleSession_optimized.js' }
        ];

        for (const check of checks) {
            if (!fs.existsSync(check.file)) {
                console.error(`❌ 文件不存在: ${check.name}`);
                return false;
            }
            console.log(`✅ 找到文件: ${check.name}`);
        }

        return true;
    }

    // 创建备份
    createBackup() {
        console.log('💾 创建备份...');
        
        try {
            if (fs.existsSync(this.backupFile)) {
                console.log('⚠️  备份文件已存在，将覆盖');
            }
            
            fs.copyFileSync(this.workerFile, this.backupFile);
            console.log(`✅ 备份创建成功: ${path.basename(this.backupFile)}`);
            return true;
        } catch (error) {
            console.error('❌ 备份创建失败:', error.message);
            return false;
        }
    }

    // 检查是否已经集成
    checkIntegration() {
        try {
            const content = fs.readFileSync(this.workerFile, 'utf8');
            return content.includes(OPTIMIZED_MARKER);
        } catch (error) {
            return false;
        }
    }

    // 集成优化版本
    integrateOptimized() {
        console.log('🔧 集成优化版本...');

        if (this.checkIntegration()) {
            console.log('⚠️  优化版本已经集成，跳过');
            return true;
        }

        try {
            let workerContent = fs.readFileSync(this.workerFile, 'utf8');
            
            // 添加导入语句
            const importStatement = `${OPTIMIZED_MARKER}\nimport { handleSessionOptimized, perfMonitor } from './handleSession_optimized.js';\n`;
            
            // 在文件开头添加导入
            if (workerContent.includes('import { connect }')) {
                workerContent = workerContent.replace(
                    'import { connect } from \'cloudflare:sockets\';',
                    `import { connect } from 'cloudflare:sockets';\n${importStatement}`
                );
            } else {
                workerContent = importStatement + workerContent;
            }

            // 替换 handleSession 调用
            const replacements = [
                {
                    original: /const handleSessionA = \(request, env, ctx\) => config\.sessionA\(request, env, ctx, globalControllerConfig\.targetProtocolType0\);/g,
                    replacement: 'const handleSessionA = (request, env, ctx) => handleSessionOptimized(request, env, ctx, globalControllerConfig.targetProtocolType0);'
                },
                {
                    original: /const handleSessionZ = \(request, env, ctx\) => config\.sessionZ\(request, env, ctx, globalControllerConfig\.targetProtocolType1\);/g,
                    replacement: 'const handleSessionZ = (request, env, ctx) => handleSessionOptimized(request, env, ctx, globalControllerConfig.targetProtocolType1);'
                }
            ];

            let replacementCount = 0;
            for (const replacement of replacements) {
                if (replacement.original.test(workerContent)) {
                    workerContent = workerContent.replace(replacement.original, replacement.replacement);
                    replacementCount++;
                }
            }

            if (replacementCount === 0) {
                console.log('⚠️  未找到标准的 handleSession 调用模式，尝试手动集成...');
                return this.manualIntegration(workerContent);
            }

            // 添加性能监控端点
            const monitoringEndpoint = this.createMonitoringEndpoint();
            workerContent = workerContent.replace(
                'case `/z`: {',
                `case '/performance': {\n${monitoringEndpoint}\n                    }\n                    case \`/z\`: {`
            );

            fs.writeFileSync(this.workerFile, workerContent);
            console.log(`✅ 集成完成，替换了 ${replacementCount} 个 handleSession 调用`);
            return true;

        } catch (error) {
            console.error('❌ 集成失败:', error.message);
            return false;
        }
    }

    // 手动集成指导
    manualIntegration(content) {
        console.log('📝 手动集成指导:');
        console.log('');
        console.log('1. 在文件顶部添加导入:');
        console.log('   import { handleSessionOptimized } from \'./handleSession_optimized.js\';');
        console.log('');
        console.log('2. 替换 handleSession 调用:');
        console.log('   将: handleSession(request, env, ctx, protocolMode)');
        console.log('   改为: handleSessionOptimized(request, env, ctx, protocolMode)');
        console.log('');
        
        // 尝试找到可能的替换位置
        const patterns = [
            /handleSession\s*\(/g,
            /config\.sessionA/g,
            /config\.sessionZ/g
        ];

        for (const pattern of patterns) {
            const matches = content.match(pattern);
            if (matches) {
                console.log(`3. 找到 ${matches.length} 个可能需要替换的位置`);
                break;
            }
        }

        return false;
    }

    // 创建性能监控端点
    createMonitoringEndpoint() {
        return `                        const stats = perfMonitor.getStats();
                        return new Response(JSON.stringify(stats, null, 2), {
                            status: 200,
                            headers: { "Content-Type": "application/json" }
                        });`;
    }

    // 恢复备份
    restoreBackup() {
        console.log('🔄 恢复备份...');
        
        if (!fs.existsSync(this.backupFile)) {
            console.error('❌ 备份文件不存在');
            return false;
        }

        try {
            fs.copyFileSync(this.backupFile, this.workerFile);
            console.log('✅ 备份恢复成功');
            return true;
        } catch (error) {
            console.error('❌ 备份恢复失败:', error.message);
            return false;
        }
    }

    // 验证集成
    validateIntegration() {
        console.log('🧪 验证集成...');
        
        try {
            const content = fs.readFileSync(this.workerFile, 'utf8');
            
            const checks = [
                { pattern: /handleSessionOptimized/g, name: '优化函数调用' },
                { pattern: /perfMonitor/g, name: '性能监控' },
                { pattern: new RegExp(OPTIMIZED_MARKER), name: '集成标记' }
            ];

            let passedChecks = 0;
            for (const check of checks) {
                if (check.pattern.test(content)) {
                    console.log(`✅ ${check.name}: 通过`);
                    passedChecks++;
                } else {
                    console.log(`❌ ${check.name}: 失败`);
                }
            }

            const success = passedChecks === checks.length;
            console.log(`\n📊 验证结果: ${passedChecks}/${checks.length} 项通过`);
            
            if (success) {
                console.log('🎉 集成验证成功！');
                this.showNextSteps();
            }

            return success;

        } catch (error) {
            console.error('❌ 验证失败:', error.message);
            return false;
        }
    }

    // 显示后续步骤
    showNextSteps() {
        console.log('\n📋 后续步骤:');
        console.log('1. 测试优化版本:');
        console.log('   node -e "import(\'./handleSession_test.js\').then(m => m.runTests())"');
        console.log('');
        console.log('2. 部署到 Cloudflare Workers');
        console.log('');
        console.log('3. 监控性能:');
        console.log('   访问 /performance 端点查看性能统计');
        console.log('');
        console.log('4. 如需回滚:');
        console.log('   node deploy_optimized.js --restore');
    }

    // 主部署流程
    async deploy() {
        console.log('🚀 开始部署优化版 handleSession...\n');

        // 检查文件
        if (!this.checkFiles()) {
            process.exit(1);
        }

        // 创建备份
        if (!this.createBackup()) {
            process.exit(1);
        }

        // 集成优化版本
        if (!this.integrateOptimized()) {
            console.log('\n❌ 集成失败，正在恢复备份...');
            this.restoreBackup();
            process.exit(1);
        }

        // 验证集成
        if (!this.validateIntegration()) {
            console.log('\n❌ 验证失败，正在恢复备份...');
            this.restoreBackup();
            process.exit(1);
        }

        console.log('\n🎉 部署完成！');
    }

    // 恢复流程
    async restore() {
        console.log('🔄 开始恢复原始版本...\n');
        
        if (this.restoreBackup()) {
            console.log('✅ 恢复完成！');
        } else {
            console.log('❌ 恢复失败！');
            process.exit(1);
        }
    }
}

// 命令行处理
async function main() {
    const args = process.argv.slice(2);
    const helper = new DeploymentHelper();

    if (args.includes('--restore') || args.includes('-r')) {
        await helper.restore();
    } else if (args.includes('--help') || args.includes('-h')) {
        console.log('使用方法:');
        console.log('  node deploy_optimized.js        # 部署优化版本');
        console.log('  node deploy_optimized.js -r     # 恢复原始版本');
        console.log('  node deploy_optimized.js -h     # 显示帮助');
    } else {
        await helper.deploy();
    }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch(console.error);
}

export { DeploymentHelper };
