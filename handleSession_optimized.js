// 优化版 handleSession - 基于性能分析的内联优化实现
// 主要优化：内联关键路径、简化模式选择、优化内存分配、简化错误处理

import { connect } from 'cloudflare:sockets';

// 全局配置 - 从原始文件复制
const globalSessionConfig = {
    connect: {
        connectMode: 'direct',
        retryMode: 'relayip',
    },
    user: {
        id: '49f37b98-c37f-4d46-be93-1fe0a742dd43',
        pass: 'a233255z',
        sha224: '419023a775279d21cdbda41971c0bb52e962f11b4f4bfba6015a268b',
    },
    relay: {
        ip: 'jp.almain126.changeip.biz',
        _port: null,
        get port() {
            if (this._port !== null) {
                return this._port;
            }
            return this.ip.includes(':') ? this.ip.split(':')[1] : (null || undefined);
        },
        set port(value) {
            this._port = value;
        },
        socks: 'web5.serv00.com:13668',
    },
};

const globalControllerConfig = {
    targetProtocolType0: 'vless',
    targetProtocolType1: 'trojan',
    connectMode: 'direct',
    retryMode: 'relayip',
};

// 预分配的UUID转换表 - 性能优化
const uuidHexTable = new Array(256);
for (let i = 0; i < 256; i++) {
    uuidHexTable[i] = (i + 256).toString(16).slice(1);
}

// 优化版 handleSession - 内联关键路径，移除抽象层
export async function handleSessionOptimized(request, env, ctx, protocolMode) {
    const { 0: client, 1: server } = Object.values(new WebSocketPair());
    server.accept();

    // 简化配置 - 固定使用最优模式
    const earlyHeader = request.headers.get("sec-websocket-protocol") || "";
    
    let tcpInterface = null;
    let tcpWriter = null;
    let tcpReader = null;

    try {
        // 内联数据流处理 - 移除TransformStream抽象层
        let headerBuffer = null;
        
        if (earlyHeader) {
            try {
                headerBuffer = decodeBase64Url(earlyHeader);
            } catch (e) {
                // 忽略早期数据解析错误
            }
        }

        // 内联消息处理 - 避免事件监听器开销
        const messageQueue = new Array(1000); // 预分配消息队列
        let queueIndex = 0;
        let headerProcessed = false;

        const processMessage = async (data) => {
            if (!headerProcessed && !headerBuffer) {
                headerBuffer = data;
            } else if (tcpWriter) {
                try {
                    await tcpWriter.write(data);
                } catch (e) {
                    // 忽略写入错误
                }
            } else {
                // 缓存消息直到TCP连接建立
                if (queueIndex < messageQueue.length) {
                    messageQueue[queueIndex++] = data;
                }
            }
        };

        server.addEventListener("message", e => processMessage(e.data));

        // 等待header数据
        if (!headerBuffer) {
            // 简化的header等待逻辑
            let attempts = 0;
            while (!headerBuffer && attempts < 100) {
                await new Promise(resolve => setTimeout(resolve, 10));
                attempts++;
            }
            if (!headerBuffer) {
                return new Response('Client closed before sending header', { status: 400 });
            }
        }

        // 内联协议解析 - 移除函数调用开销
        let header;
        try {
            header = await parseHeaderInline(headerBuffer, server, protocolMode);
        } catch (e) {
            return new Response(`Header parse error: ${e.message}`, { status: 400 });
        }

        // 内联连接建立 - 简化连接逻辑
        try {
            tcpInterface = await createConnectionInline(header, globalControllerConfig.connectMode, protocolMode);
        } catch {
            try {
                tcpInterface = await createConnectionInline(header, globalControllerConfig.retryMode, protocolMode);
            } catch (e) {
                return new Response(`Connection failed: ${e.message}`, { status: 400 });
            }
        }

        tcpWriter = tcpInterface.writable.getWriter();
        tcpReader = tcpInterface.readable.getReader();

        // 写入初始数据
        if (header.rawClientData) {
            try {
                await tcpWriter.write(header.rawClientData);
            } catch (e) {
                // 忽略初始数据写入错误
            }
        }

        // 处理队列中的消息
        for (let i = 0; i < queueIndex; i++) {
            try {
                await tcpWriter.write(messageQueue[i]);
            } catch (e) {
                // 忽略队列消息写入错误
            }
        }

        // 内联下游数据泵 - 最优性能的reader模式
        const pumpDownstream = async () => {
            try {
                for (;;) {
                    const { value: chunk, done } = await tcpReader.read();
                    if (done) break;
                    try {
                        server.send(chunk);
                    } catch (e) {
                        if (e instanceof TypeError) break;
                        throw e;
                    }
                }
            } finally {
                tcpReader?.releaseLock();
            }
        };

        await pumpDownstream();

    } catch (e) {
        return new Response(`Session error: ${e.message}`, { status: 500 });
    } finally {
        // 简化的资源清理
        const cleanupPromises = new Array(3); // 预分配清理数组
        let cleanupIndex = 0;

        if (tcpWriter) {
            cleanupPromises[cleanupIndex++] = tcpWriter.releaseLock().catch(() => {});
        }
        if (tcpReader) {
            cleanupPromises[cleanupIndex++] = tcpReader.releaseLock().catch(() => {});
        }
        if (tcpInterface) {
            cleanupPromises[cleanupIndex++] = tcpInterface.close().catch(() => {});
        }

        // 非阻塞清理
        Promise.allSettled(cleanupPromises.slice(0, cleanupIndex));

        try {
            server.close(1000);
        } catch (e) {}
    }

    return new Response(null, { status: 101, webSocket: client });
}

// 内联协议解析函数 - 移除抽象层
async function parseHeaderInline(buffer, wsInterface, protocolMode) {
    const bytes = new Uint8Array(buffer);
    const view = new DataView(buffer);
    const decoder = new TextDecoder();

    if (protocolMode === globalControllerConfig.targetProtocolType0) {
        // VLESS协议解析 - 内联优化
        const extractedID = bytes.subarray(1, 17);
        if (!matchUuidOptimized(extractedID, globalSessionConfig.user.id)) {
            wsInterface.close(1013, 'Invalid user');
            throw new Error('Invalid user: UUID does not match');
        }

        let offset = 17;
        const optLength = bytes[offset++];
        offset += optLength;
        const command = bytes[offset++];
        const port = view.getUint16(offset);
        offset += 2;
        const addressType = bytes[offset++];

        let hostname = '';
        switch (addressType) {
            case 1: // IPv4
                hostname = `${bytes[offset]}.${bytes[offset + 1]}.${bytes[offset + 2]}.${bytes[offset + 3]}`;
                offset += 4;
                break;
            case 2: // Domain
                const domainLength = bytes[offset++];
                hostname = decoder.decode(bytes.subarray(offset, offset + domainLength));
                offset += domainLength;
                break;
            case 3: // IPv6
                const ipv6Parts = new Array(8);
                for (let i = 0; i < 8; i++) {
                    ipv6Parts[i] = view.getUint16(offset + i * 2).toString(16);
                }
                hostname = ipv6Parts.join(':');
                offset += 16;
                break;
            default:
                throw new Error('Invalid address type');
        }

        const rawClientData = bytes.subarray(offset);
        wsInterface.send(Uint8Array.of(0, 0).buffer);

        return {
            addressType,
            addressRemote: hostname,
            portRemote: port,
            rawClientData
        };
    } else {
        // Trojan协议解析 - 内联优化
        const crLfIndex = 56;
        const extractedPassword = decoder.decode(bytes.subarray(0, crLfIndex));
        if (extractedPassword !== globalSessionConfig.user.sha224) {
            wsInterface.close(1013, 'Invalid password');
            throw new Error('Invalid password');
        }

        let offset = crLfIndex + 2;
        const command = bytes[offset++];
        const addressType = bytes[offset++];

        let hostname = '';
        switch (addressType) {
            case 1: // IPv4
                hostname = `${bytes[offset]}.${bytes[offset + 1]}.${bytes[offset + 2]}.${bytes[offset + 3]}`;
                offset += 4;
                break;
            case 3: // Domain
                const domainLength = bytes[offset++];
                hostname = decoder.decode(bytes.subarray(offset, offset + domainLength));
                offset += domainLength;
                break;
            case 4: // IPv6
                const ipv6Parts = new Array(8);
                for (let i = 0; i < 8; i++) {
                    ipv6Parts[i] = view.getUint16(offset + i * 2).toString(16);
                }
                hostname = ipv6Parts.join(':');
                offset += 16;
                break;
            default:
                throw new Error('Invalid address type');
        }

        const port = view.getUint16(offset);
        offset += 4;
        const rawClientData = bytes.subarray(offset);

        return {
            addressType,
            addressRemote: hostname,
            portRemote: port,
            rawClientData
        };
    }
}

// 优化的UUID匹配函数
function matchUuidOptimized(extractedID, uuidString) {
    const cleanUuid = uuidString.replaceAll('-', '');
    for (let index = 0; index < 16; index++) {
        const expected = parseInt(cleanUuid.substring(index * 2, index * 2 + 2), 16);
        if (extractedID[index] !== expected) {
            return false;
        }
    }
    return true;
}

// 内联连接创建函数 - 支持SOCKS5
async function createConnectionInline(header, mode, protocolMode) {
    const { addressType, addressRemote, portRemote } = header;
    const useTargetProtocol = protocolMode === globalControllerConfig.targetProtocolType0;

    switch (mode) {
        case 'relayip': {
            const needDirect =
                [1].includes(addressType) ||
                (useTargetProtocol && [3].includes(addressType)) ||
                (!useTargetProtocol && [4].includes(addressType));

            const connection = needDirect
                ? connect({ hostname: addressRemote, port: portRemote })
                : connect({
                    hostname: globalSessionConfig.relay.ip,
                    port: globalSessionConfig.relay.port || portRemote,
                });

            await connection.opened;
            return connection;
        }
        case 'relaysocks': {
            return await socks5ConnectInline(addressType, addressRemote, portRemote, useTargetProtocol);
        }
        case 'direct':
        default: {
            const connection = connect({ hostname: addressRemote, port: portRemote });
            await connection.opened;
            return connection;
        }
    }
}

// Base64URL解码函数
function decodeBase64Url(encodedString) {
    return Uint8Array.from(atob(encodedString.replaceAll('-', '+').replaceAll('_', '/')), (c) => c.charCodeAt(0)).buffer;
}

// 内联SOCKS5连接函数 - 简化版本
async function socks5ConnectInline(addressType, addressRemote, portRemote, useTargetProtocol) {
    const socksConfig = globalSessionConfig.relay.socks;
    const [userPass, hostPort] = socksConfig.includes('@') ? socksConfig.split('@') : ['', socksConfig];
    const [username, password] = userPass ? userPass.split(':') : ['', ''];
    const [hostname, port] = hostPort.split(':');

    const socket = connect({ hostname, port: parseInt(port) });
    await socket.opened;

    const writer = socket.writable.getWriter();
    const reader = socket.readable.getReader();
    const encoder = new TextEncoder();

    try {
        // SOCKS5认证
        const authMethods = new Uint8Array([5, 2, 0, 2]);
        await writer.write(authMethods);

        const authResponse = (await reader.read()).value;
        if (authResponse[1] === 0x02 && username && password) {
            const credentials = new Uint8Array([
                1, username.length, ...encoder.encode(username),
                password.length, ...encoder.encode(password)
            ]);
            await writer.write(credentials);

            const credResponse = (await reader.read()).value;
            if (credResponse[0] !== 0x01 || credResponse[1] !== 0x00) {
                throw new Error('SOCKS5 authentication failed');
            }
        }

        // 构建连接请求
        let addressBytes;
        switch (addressType) {
            case 1: // IPv4
                addressBytes = new Uint8Array([1, ...addressRemote.split('.').map(Number)]);
                break;
            case 2: // Domain
                addressBytes = new Uint8Array([3, addressRemote.length, ...encoder.encode(addressRemote)]);
                break;
            case 3: // IPv6
                addressBytes = new Uint8Array([4, ...addressRemote.split(':').flatMap(x => [
                    parseInt(x.slice(0, 2), 16), parseInt(x.slice(2), 16)
                ])]);
                break;
            default:
                throw new Error('Invalid address type for SOCKS5');
        }

        const connectRequest = new Uint8Array([
            5, 1, 0, ...addressBytes, portRemote >> 8, portRemote & 0xff
        ]);
        await writer.write(connectRequest);

        const connectResponse = (await reader.read()).value;
        if (connectResponse[0] !== 0x05 || connectResponse[1] !== 0x00) {
            throw new Error('SOCKS5 connection failed');
        }

        writer.releaseLock();
        reader.releaseLock();
        return socket;

    } catch (error) {
        writer.releaseLock();
        reader.releaseLock();
        socket.close();
        throw error;
    }
}

// 性能监控和统计 - 可选功能
class PerformanceMonitor {
    constructor() {
        this.stats = {
            connections: 0,
            errors: 0,
            avgLatency: 0,
            memoryUsage: 0
        };
        this.startTime = Date.now();
    }

    recordConnection() {
        this.stats.connections++;
    }

    recordError() {
        this.stats.errors++;
    }

    getStats() {
        return {
            ...this.stats,
            uptime: Date.now() - this.startTime,
            errorRate: this.stats.errors / Math.max(this.stats.connections, 1)
        };
    }
}

// 全局性能监控实例
const perfMonitor = new PerformanceMonitor();

// 导出优化版本和性能监控
export { handleSessionOptimized as default, perfMonitor };
